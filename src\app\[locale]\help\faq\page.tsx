'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import Layout from '../../../components/Layout';

export default function FAQPage() {
  const t = useTranslations('help.faq');
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const faqItems = t.raw('items') || [];

  return (
    <Layout>
      <div className="min-h-screen">
        {/* 英雄区域 */}
        <section className="relative py-16 md:py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-indigo-900/10 to-purple-900/20" />
          <div className="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10" />
          
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                <span className="bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 bg-clip-text text-transparent">
                  {t('title')}
                </span>
              </h1>
              <p className="text-lg sm:text-xl md:text-2xl text-zinc-300 mb-8 leading-relaxed">
                {t('subtitle')}
              </p>
            </div>
          </div>
        </section>

        {/* FAQ内容 */}
        <section className="py-16 md:py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="space-y-4">
              {faqItems.map((item: any, index: number) => (
                <div key={index} className="group">
                  <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-2xl overflow-hidden transition-all duration-300 hover:border-indigo-500/50 hover:shadow-lg hover:shadow-indigo-500/10">
                    <button
                      onClick={() => toggleFAQ(index)}
                      className="w-full px-6 sm:px-8 py-6 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:ring-inset"
                    >
                      <h3 className="text-lg sm:text-xl font-semibold text-white group-hover:text-indigo-300 transition-colors pr-4">
                        {item.question}
                      </h3>
                      <div className={`flex-shrink-0 w-6 h-6 text-indigo-400 transition-transform duration-300 ${openIndex === index ? 'rotate-180' : ''}`}>
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </button>
                    
                    <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                      openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                    }`}>
                      <div className="px-6 sm:px-8 pb-6">
                        <div className="border-t border-zinc-700/50 pt-4">
                          <p className="text-zinc-300 leading-relaxed">
                            {item.answer}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 联系客服 */}
        <section className="py-16 md:py-20 bg-zinc-900/50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-3xl p-8 md:p-12">
              <div className="w-16 h-16 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h2 className="text-2xl sm:text-3xl font-bold text-white mb-4">
                {useTranslations('help.notFound')('title')}
              </h2>
              <p className="text-lg text-zinc-300 mb-8">
                {useTranslations('help.notFound')('description')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white font-semibold rounded-xl hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-indigo-500/25">
                  {useTranslations('help.notFound')('onlineSupport')}
                </button>
                <a
                  href="mailto:<EMAIL>"
                  className="px-8 py-3 bg-zinc-700/50 text-white font-semibold rounded-xl border border-zinc-600/50 hover:bg-zinc-600/50 hover:border-zinc-500/50 transition-all duration-300 hover:scale-105"
                >
                  {useTranslations('help.notFound')('sendEmail')}
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
