import { useTranslations } from 'next-intl';
import Layout from '../../../components/Layout';

export default function RefundPolicyPage() {
  const t = useTranslations('help.refundPolicy');

  const conditions = t.raw('conditions.items') || [];
  const processSteps = t.raw('process.steps') || [];
  const contactMethods = t.raw('contact.methods') || [];

  return (
    <Layout>
      <div className="min-h-screen">
        {/* 英雄区域 */}
        <section className="relative py-16 md:py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/10 to-rose-900/20" />
          <div className="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10" />
          
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-rose-400 bg-clip-text text-transparent">
                  {t('title')}
                </span>
              </h1>
              <p className="text-lg sm:text-xl md:text-2xl text-zinc-300 mb-8 leading-relaxed">
                {t('subtitle')}
              </p>
            </div>
          </div>
        </section>

        {/* 退款概述 */}
        <section className="py-16 md:py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-3xl p-8 md:p-12 mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-white mb-6">
                {t('overview.title')}
              </h2>
              <p className="text-lg text-zinc-300 leading-relaxed">
                {t('overview.description')}
              </p>
            </div>
          </div>
        </section>

        {/* 退款条件 */}
        <section className="py-16 md:py-20 bg-zinc-900/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 md:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-4">
                {t('conditions.title')}
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
              {conditions.map((condition: any, index: number) => (
                <div key={index} className="group">
                  <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-2xl p-6 sm:p-8 h-full transition-all duration-300 hover:border-purple-500/50 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-sm">{index + 1}</span>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors">
                      {condition.title}
                    </h3>
                    <p className="text-zinc-400 leading-relaxed">
                      {condition.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 退款流程 */}
        <section className="py-16 md:py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 md:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-4">
                {t('process.title')}
              </h2>
            </div>

            <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-3xl p-8 md:p-12">
              <div className="space-y-6">
                {processSteps.map((step: string, index: number) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white font-bold text-sm">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-zinc-300 leading-relaxed">
                        {step}
                      </p>
                      {index < processSteps.length - 1 && (
                        <div className="w-px h-6 bg-gradient-to-b from-purple-500/50 to-transparent ml-4 mt-4"></div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* 申请时限 */}
        <section className="py-16 md:py-20 bg-zinc-900/50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-3xl p-8 md:p-12">
              <div className="flex items-start space-x-6">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-xl flex items-center justify-center flex-shrink-0">
                  <svg className="w-8 h-8 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-2xl sm:text-3xl font-bold text-white mb-4">
                    {t('timeLimit.title')}
                  </h2>
                  <p className="text-lg text-zinc-300 leading-relaxed">
                    {t('timeLimit.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* 联系方式 */}
        <section className="py-16 md:py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 md:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-4">
                {t('contact.title')}
              </h2>
              <p className="text-lg text-zinc-300 max-w-2xl mx-auto">
                {t('contact.description')}
              </p>
            </div>

            <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-3xl p-8 md:p-12">
              <div className="space-y-6">
                {contactMethods.map((method: string, index: number) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <p className="text-zinc-300 leading-relaxed">
                      {method}
                    </p>
                  </div>
                ))}
              </div>

              <div className="mt-8 pt-8 border-t border-zinc-700/50 text-center">
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button className="px-8 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25">
                    {useTranslations('help.notFound')('onlineSupport')}
                  </button>
                  <a
                    href="mailto:<EMAIL>"
                    className="px-8 py-3 bg-zinc-700/50 text-white font-semibold rounded-xl border border-zinc-600/50 hover:bg-zinc-600/50 hover:border-zinc-500/50 transition-all duration-300 hover:scale-105"
                  >
                    {useTranslations('help.notFound')('sendEmail')}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
